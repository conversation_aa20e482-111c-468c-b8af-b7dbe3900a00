package com.ruoyi.miniapp.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 小程序首页功能模块对象 mini_home_module
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
public class MiniHomeModule extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 模块名称 */
    @Excel(name = "模块名称")
    private String moduleName;

    /** 模块图标URL */
    @Excel(name = "模块图标")
    private String moduleIcon;

    /** 模块代码（唯一标识） */
    @Excel(name = "模块代码")
    private String moduleCode;

    /** 模块跳转链接 */
    @Excel(name = "内部链接")
    private String moduleUrl;

    /** 链接类型（1内部页面 2外部链接） */
    @Excel(name = "链接类型", readConverterExp = "1=内部页面,2=外部链接")
    private String linkType;

    /** 外部链接URL */
    @Excel(name = "外部链接")
    private String externalUrl;

    /** 排序（数字越小越靠前） */
    @Excel(name = "排序")
    private Integer sortOrder;

    /** 是否启用（0否 1是） */
    @Excel(name = "是否启用", readConverterExp = "0=否,1=是")
    private String isEnabled;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    
    public void setModuleName(String moduleName) 
    {
        this.moduleName = moduleName;
    }

    public String getModuleName() 
    {
        return moduleName;
    }
    
    public void setModuleIcon(String moduleIcon) 
    {
        this.moduleIcon = moduleIcon;
    }

    public String getModuleIcon() 
    {
        return moduleIcon;
    }
    
    public void setModuleCode(String moduleCode) 
    {
        this.moduleCode = moduleCode;
    }

    public String getModuleCode() 
    {
        return moduleCode;
    }
    
    public void setModuleUrl(String moduleUrl) 
    {
        this.moduleUrl = moduleUrl;
    }

    public String getModuleUrl() 
    {
        return moduleUrl;
    }
    
    public void setLinkType(String linkType) 
    {
        this.linkType = linkType;
    }

    public String getLinkType() 
    {
        return linkType;
    }
    
    public void setExternalUrl(String externalUrl) 
    {
        this.externalUrl = externalUrl;
    }

    public String getExternalUrl() 
    {
        return externalUrl;
    }
    
    public void setSortOrder(Integer sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }
    
    public void setIsEnabled(String isEnabled) 
    {
        this.isEnabled = isEnabled;
    }

    public String getIsEnabled() 
    {
        return isEnabled;
    }
    
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("moduleName", getModuleName())
            .append("moduleIcon", getModuleIcon())
            .append("moduleCode", getModuleCode())
            .append("moduleUrl", getModuleUrl())
            .append("linkType", getLinkType())
            .append("externalUrl", getExternalUrl())
            .append("sortOrder", getSortOrder())
            .append("isEnabled", getIsEnabled())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
