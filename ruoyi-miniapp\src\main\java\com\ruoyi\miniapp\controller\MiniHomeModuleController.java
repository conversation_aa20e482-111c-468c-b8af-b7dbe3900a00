package com.ruoyi.miniapp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.miniapp.domain.MiniHomeModule;
import com.ruoyi.miniapp.service.IMiniHomeModuleService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 小程序首页功能模块Controller
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
@Api(tags = "小程序首页功能模块管理")
@RestController
@RequestMapping("/miniapp/homemodule")
public class MiniHomeModuleController extends BaseController
{
    @Autowired
    private IMiniHomeModuleService miniHomeModuleService;

    /**
     * 查询小程序首页功能模块列表
     */
    @ApiOperation("查询小程序首页功能模块列表")
    @PreAuthorize("@ss.hasPermi('config:homemodule:list')")
    @GetMapping("/list")
    public TableDataInfo list(MiniHomeModule miniHomeModule)
    {
        startPage();
        List<MiniHomeModule> list = miniHomeModuleService.selectMiniHomeModuleList(miniHomeModule);
        return getDataTable(list);
    }

    /**
     * 导出小程序首页功能模块列表
     */
    @ApiOperation("导出小程序首页功能模块列表")
    @PreAuthorize("@ss.hasPermi('config:homemodule:export')")
    @Log(title = "小程序首页功能模块", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MiniHomeModule miniHomeModule)
    {
        List<MiniHomeModule> list = miniHomeModuleService.selectMiniHomeModuleList(miniHomeModule);
        ExcelUtil<MiniHomeModule> util = new ExcelUtil<MiniHomeModule>(MiniHomeModule.class);
        util.exportExcel(response, list, "小程序首页功能模块数据");
    }

    /**
     * 获取小程序首页功能模块详细信息
     */
    @ApiOperation("获取小程序首页功能模块详细信息")
    @PreAuthorize("@ss.hasPermi('config:homemodule:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam("模块ID") @PathVariable("id") Long id)
    {
        return AjaxResult.success(miniHomeModuleService.selectMiniHomeModuleById(id));
    }

    /**
     * 新增小程序首页功能模块
     */
    @ApiOperation("新增小程序首页功能模块")
    @PreAuthorize("@ss.hasPermi('config:homemodule:add')")
    @Log(title = "小程序首页功能模块", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam("小程序首页功能模块") @RequestBody MiniHomeModule miniHomeModule)
    {
        if (!miniHomeModuleService.checkModuleCodeUnique(miniHomeModule))
        {
            return AjaxResult.error("新增模块'" + miniHomeModule.getModuleName() + "'失败，模块代码已存在");
        }
        miniHomeModule.setCreateBy(getUsername());
        return toAjax(miniHomeModuleService.insertMiniHomeModule(miniHomeModule));
    }

    /**
     * 修改小程序首页功能模块
     */
    @ApiOperation("修改小程序首页功能模块")
    @PreAuthorize("@ss.hasPermi('config:homemodule:edit')")
    @Log(title = "小程序首页功能模块", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam("小程序首页功能模块") @RequestBody MiniHomeModule miniHomeModule)
    {
        if (!miniHomeModuleService.checkModuleCodeUnique(miniHomeModule))
        {
            return AjaxResult.error("修改模块'" + miniHomeModule.getModuleName() + "'失败，模块代码已存在");
        }
        miniHomeModule.setUpdateBy(getUsername());
        return toAjax(miniHomeModuleService.updateMiniHomeModule(miniHomeModule));
    }

    /**
     * 删除小程序首页功能模块
     */
    @ApiOperation("删除小程序首页功能模块")
    @PreAuthorize("@ss.hasPermi('config:homemodule:remove')")
    @Log(title = "小程序首页功能模块", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@ApiParam("模块ID数组") @PathVariable Long[] ids)
    {
        return toAjax(miniHomeModuleService.deleteMiniHomeModuleByIds(ids));
    }

    /**
     * 获取启用的小程序首页功能模块列表
     */
    @ApiOperation("获取启用的小程序首页功能模块列表")
    @GetMapping("/enabled")
    public AjaxResult getEnabledList()
    {
        List<MiniHomeModule> list = miniHomeModuleService.selectEnabledMiniHomeModuleList();
        return AjaxResult.success(list);
    }

    /**
     * 校验模块代码
     */
    @ApiOperation("校验模块代码")
    @PostMapping("/checkModuleCodeUnique")
    public AjaxResult checkModuleCodeUnique(@ApiParam("小程序首页功能模块") @RequestBody MiniHomeModule miniHomeModule)
    {
        return AjaxResult.success(miniHomeModuleService.checkModuleCodeUnique(miniHomeModule));
    }

    // ==================== 小程序端接口 ====================

    /**
     * 获取小程序首页功能模块列表（小程序端）
     */
    @ApiOperation("获取小程序首页功能模块列表")
    @GetMapping("/app/getModuleList")
    public AjaxResult getModuleListForApp()
    {
        List<MiniHomeModule> list = miniHomeModuleService.selectActiveModuleList();
        return AjaxResult.success(list);
    }

    /**
     * 根据模块代码获取模块详情（小程序端）
     */
    @ApiOperation("根据模块代码获取模块详情")
    @GetMapping("/app/getByCode/{moduleCode}")
    public AjaxResult getByModuleCode(@ApiParam("模块代码") @PathVariable("moduleCode") String moduleCode)
    {
        MiniHomeModule module = miniHomeModuleService.selectMiniHomeModuleByModuleCode(moduleCode);
        if (module == null || !"0".equals(module.getStatus()) || !"1".equals(module.getIsEnabled())) {
            return AjaxResult.error("模块不存在或已停用");
        }
        return AjaxResult.success(module);
    }
}
