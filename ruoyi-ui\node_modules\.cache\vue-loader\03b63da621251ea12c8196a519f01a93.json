{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\config\\homemodule\\index.vue?vue&type=template&id=a19f53a8", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\config\\homemodule\\index.vue", "mtime": 1754294444980}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751437916212}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}