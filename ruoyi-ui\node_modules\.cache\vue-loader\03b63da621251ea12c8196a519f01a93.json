{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\config\\homemodule\\index.vue?vue&type=template&id=a19f53a8", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\config\\homemodule\\index.vue", "mtime": 1754294380517}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751437916212}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}